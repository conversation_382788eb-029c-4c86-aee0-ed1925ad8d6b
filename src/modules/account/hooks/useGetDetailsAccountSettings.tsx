import accountSettingsRequest from "@/modules/account/api/account.api";
import React from "react";

const useGetDetailsAccountSettings = () => {
  async function request(
    setLoading: Function,
    onSuccess: Function,
    onError: Function
  ) {
    setLoading(true);
    const response = await accountSettingsRequest.getDetailsAccountSettings();
    if (response?.status === 200) {
      onSuccess(response.data);
      setLoading(false);
    } else {
      onError(response);
      setLoading(false);
    }
  }

  return [request];
};

export default useGetDetailsAccountSettings;
