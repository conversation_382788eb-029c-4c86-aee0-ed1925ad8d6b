import modelRequest from "@/modules/model/api/model.api";
import { useState } from "react";

export const useGetAllService = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  async function requestGetAllService(params: {
    keyword?: string;
    skipCount?: number;
    maxResultCount?: number;
  }) {
    setLoading(true);
    const response = await modelRequest.getAllService(params);
    if (response?.status === 200) {
      setData(
        response.data && response.data.result && response.data.result.items
          ? response.data.result.items
          : []
      );
      setLoading(false);
    } else {
      setLoading(false);
      return null;
    }
  }

  return {
    requestGetAllService,
    data,
    loading,
  };
};
