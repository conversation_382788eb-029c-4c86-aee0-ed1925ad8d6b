import userRequest from "@/modules/account/api/user.api";
import { AccountBodyType } from "@/modules/account/model/schema/account.schema";

export const useCreateUser = () => {
  async function request(
    params: AccountBodyType,
    setLoading: Function,
    onSuccess: Function,
    onError: Function
  ) {
    try {
      setLoading(true);
      const response = await userRequest.createUser({
        ...params,
        isActive: params.isActive === true ? true : false,
      });
      if (response.status === 200) {
        onSuccess(response.data);
        setLoading(false);
      } else {
      }
    } catch (error) {
      onError(error);
      setLoading(false);
    }
  }
  return [request];
};
