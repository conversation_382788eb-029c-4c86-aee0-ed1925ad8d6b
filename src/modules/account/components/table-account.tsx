"use client";
import { useGetListUserAdmin } from "@/modules/account-management/hooks/useGetListUserAdmin.hook";
import { AccountBodyType } from "@/modules/account/model/schema/account.schema";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import type { TableColumnsType } from "antd";
import { Switch, Tag } from "antd";
import type { TablePaginationConfig } from "antd/es/table/interface";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { ModalUser } from "./modal-user";
import { useGetListUserSystemAdmin } from "@/modules/account-management/hooks/useGetListUserSystemAdmin.hook";
import { IconSearch } from "@tabler/icons-react";
import { TableCommon } from "@/components/common/table-common";
import ModalSetAdmin from "@/modules/account-management/components/modal-set-admin";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { createStyles } from "antd-style";
let timeout: any;

const TableTAccount = ({ role }: any) => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { replace } = useRouter();
  //get all query params
  let querySearch: any;
  searchParams.forEach((value, key) => {
    querySearch = {
      ...querySearch,
      [key]: value,
    };
  });
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    type: "create" | "update" | "delete";
    detailInfo: any;
  }>({
    isOpen: false,
    type: "create",
    detailInfo: undefined,
  });
  const [modalStateSetAdmin, setModalStateSetAdmin] = useState<any>({
    isOpen: false,
    detailInfo: undefined,
  });
  const isSystemAdmin = role === "Company";

  //state
  const [total, setTotal] = useState(0);
  const [keyword, setKeyword] = useState<string | undefined>(
    searchParams.get("Keyword")?.toString()
  );
  const [loading, setLoading] = useState(false);
  const [dataUser, setDataUser] = useState<AccountBodyType[]>([]);
  const [requestGetListUserAdmin] = useGetListUserAdmin();
  const [requestGetListUserSystemAdmin] = useGetListUserSystemAdmin();
  const emailUser = useAppSelector((state) => state.user.userInfo.emailaddress);
  const fetchListUser = (
    params = {
      keyword: querySearch?.Keyword,
      isActive: querySearch?.isActive,
      skipCount: querySearch?.skipCount,
      maxResultCount: querySearch?.maxResultCount,
    }
  ) => {
    if (isSystemAdmin) {
      requestGetListUserSystemAdmin(
        params,
        setLoading,
        (res: any) => {
          setTotal(res.result.totalRecords);
          setDataUser(res.result.items);
        },
        (err: any) => {
          console.log(err);
        }
      );
    } else {
      requestGetListUserAdmin(
        params,
        setLoading,
        (res: any) => {
          setTotal(res.result.totalCount);
          setDataUser(res.result.items);
        },
        (err: any) => {
          console.log(err);
        }
      );
    }
  };
  useEffect(() => {
    fetchListUser();
  }, []);

  //table
  const columnsAdmin: TableColumnsType<AccountBodyType> = [
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      width: 100,
      render: (_, record, index) => {
        const isAdmin = record.roleNames?.includes("Admin");
        if (isAdmin) return null;
        return (
          <div className="flex gap-3">
            <EditOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "update",
                  detailInfo: record,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />

            {emailUser !== record.emailAddress && (
              <Switch
                onChange={() => {
                  setModalStateSetAdmin({
                    isOpen: true,
                    detailInfo: record,
                  });
                }}
                checkedChildren="Admin"
                unCheckedChildren="User"
                checked={record?.roleNames?.includes("Company")}
              />
            )}
          </div>
        );
      },
    },

    {
      title: "First Name",
      dataIndex: "firstName",
      key: "firstName",
      width: 130,
      render: (value, record, index) => {
        return <div className="capitalize">{value}</div>;
      },
    },
    {
      title: "Last Name",
      dataIndex: "lastName",
      key: "lastName",
      width: 130,
      render: (value, record, index) => {
        return <div className="capitalize">{value}</div>;
      },
    },
    {
      title: "Email",
      dataIndex: "emailAddress",
      key: "email",
    },
    {
      title: "User Name",
      dataIndex: "userName",
      key: "userName",
    },

    {
      title: "Account",
      dataIndex: "companyName",
      key: "companyName",
      render: (value, record, index) => {
        return (
          <div className="flex gap-2 ">
            <span className="capitalize">{value}</span>
          </div>
        );
      },
    },
    {
      title: "Status",
      dataIndex: "isActive",
      key: "isActive",
      width: 100,
      render: (status) =>
        status ? (
          <Tag
            icon={<CheckCircleOutlined />}
            color="success"
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
          >
            Active
          </Tag>
        ) : (
          <Tag
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
            icon={<CloseCircleOutlined />}
            color="error"
          >
            Inactive
          </Tag>
        ),
    },
  ];
  const columnsSystemAdmin: TableColumnsType<AccountBodyType> = [
    {
      title: "Email",
      dataIndex: "emailAddress",
      key: "emailAddress",
      width: 130,
    },
    {
      title: "Username",
      dataIndex: "userName",
      key: "userName",
      width: 130,
    },
    {
      title: "Role",
      dataIndex: "roleNames",
      key: "roleNames",
      width: 100,
      render: (value, record, index) => {
        const role =
          value && value?.length === 0
            ? "viewer"
            : value[0] === "Company"
            ? "Company"
            : "editor";
        return (
          <div className="flex gap-2 ">
            {role === "editor" && (
              <div className="badge badge-neutral">editor</div>
            )}
            {role === "viewer" && (
              <div className="badge badge-primary">Viewer</div>
            )}
            {role === "Company" && (
              <div className="badge badge-secondary text-white">
                System Admin
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: "Status",
      dataIndex: "isActive",
      key: "isActive",
      width: 100,
      render: (status, record, index) => {
        return status ? (
          <Tag
            icon={<CheckCircleOutlined />}
            color="success"
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
          >
            Active
          </Tag>
        ) : (
          <Tag
            style={{
              fontFamily: "Visby",
              fontWeight: 500,
              borderRadius: 20,
            }}
            icon={<CloseCircleOutlined />}
            color="error"
          >
            Inactive
          </Tag>
        );
      },
    },
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      width: 100,
      render: (_, record, index) => {
        const isSystemAdmin = record.roleNames?.includes("Company");
        if (isSystemAdmin) return null;
        return (
          <div className="flex gap-3">
            <EditOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "update",
                  detailInfo: record,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
            <DeleteOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "delete",
                  detailInfo: record,
                })
              }
              className="hover:text-primary cursor-pointer"
              style={{ fontSize: 16 }}
            />
          </div>
        );
      },
    },
  ];

  const buttons = [
    {
      title: "All",
      isActveString: undefined,
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.delete("isActive");
        params.delete("Keyword");
        params.delete("skipCount");
        setKeyword("");
        replace(`${pathname}?${params.toString()}`);
        fetchListUser({
          keyword: undefined,
          isActive: undefined,
          skipCount: 0,
          maxResultCount: querySearch?.maxResultCount,
        });
      },
    },
    {
      title: "Active",
      isActveString: "true",
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.set("isActive", "true");
        params.delete("Keyword");
        params.delete("skipCount");
        setKeyword("");
        replace(`${pathname}?${params.toString()}`);
        fetchListUser({
          keyword: undefined,
          isActive: true,
          skipCount: 0,
          maxResultCount: querySearch?.maxResultCount,
        });
      },
    },
    {
      title: "Inactive",
      isActveString: "false",
      onclick: () => {
        const params = new URLSearchParams(searchParams);
        params.set("isActive", "false");
        params.delete("Keyword");
        params.delete("skipCount");
        setKeyword("");
        replace(`${pathname}?${params.toString()}`);
        fetchListUser({
          keyword: undefined,
          isActive: false,
          skipCount: 0,
          maxResultCount: querySearch?.maxResultCount,
        });
      },
    },
  ];

  const handleSearch = (term: string) => {
    setKeyword(term);
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      const params = new URLSearchParams(searchParams);
      if (term) {
        params.set("Keyword", term);
        params.set("skipCount", "0");
      } else {
        params.delete("Keyword");
        params.set("skipCount", "0");
      }
      replace(`${pathname}?${params.toString()}`);
      fetchListUser({
        keyword: term,
        isActive: querySearch?.isActive,
        skipCount: 0,
        maxResultCount: querySearch?.maxResultCount,
      });
    }, 500);
  };

  const handleTableChange = (pagination: TablePaginationConfig) => {
    const params = new URLSearchParams(searchParams);
    params.set(
      "skipCount",
      `${((pagination.current || 1) - 1) * maxResultCount}`
    );
    params.set("maxResultCount", maxResultCount.toString());
    replace(`${pathname}?${params.toString()}`);
    fetchListUser({
      keyword: querySearch?.Keyword,
      isActive: querySearch?.isActive,
      skipCount: ((pagination.current || 1) - 1) * maxResultCount,
      maxResultCount: querySearch?.maxResultCount,
    });
  };

  let skipCount = Number(searchParams.get("skipCount")) || 1;
  let maxResultCount = Number(searchParams.get("maxResultCount")) || 50;
  let current = Math.ceil(skipCount / maxResultCount);
  if (skipCount % maxResultCount === 0) {
    current += 1;
  }
  const fontSize = useAppSelector((state) => state.user.fontSize);
  return (
    <>
      {modalStateSetAdmin.isOpen && (
        <ModalSetAdmin
          modalState={modalStateSetAdmin}
          setModalState={setModalStateSetAdmin}
          fetchListUser={fetchListUser}
        />
      )}
      {modalState.isOpen && (
        <ModalUser
          fetchListUser={fetchListUser}
          modalState={modalState}
          setModalState={setModalState}
          isSystemAdmin={isSystemAdmin}
        />
      )}
      <div className="flex flex-col gap-5">
        <p className="text-34-34 font-semibold">Users</p>
        <hr />
        <div className="">
          <div className="flex justify-between gap-2">
            <div className="px-5 rounded-lg flex items-center gap-2 h-[38px] w-[400px] bg-white border">
              <IconSearch />
              <input
                type="text"
                placeholder="Search"
                className="w-full font-normal  outline-none text-primary placeholder:text-gray80"
                onChange={(e) => {
                  handleSearch(e.target.value);
                }}
                value={keyword}
              />
            </div>
            <div className="flex gap-2">
              {buttons.map((button, index) => {
                let className: string = "";
                const isActiveSearchParam = searchParams.get("isActive");
                if (isActiveSearchParam === null && button.title === "All") {
                  className = "btn-primary btn-active";
                }
                if (isActiveSearchParam === button.isActveString) {
                  className = "btn-primary btn-active";
                }
                return (
                  <button
                    key={index}
                    className={`btn btn-sm ${className}`}
                    onClick={button.onclick}
                  >
                    {button.title}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
        <TableCommon
          style={{
            fontSize: `${fontSize}px`,
          }}
          pagination={{
            pageSize: Number(searchParams.get("maxResultCount")) || 50,
            current,
            total,
            // showQuickJumper: true,
          }}
          loading={loading}
          onChange={handleTableChange}
          columns={isSystemAdmin ? columnsSystemAdmin : (columnsAdmin as any)}
          dataSource={dataUser}
          scroll={{ x: "max-content", y: 6 * 110 }}
          footer={() => (
            <div className="justify-center my-2 ">
              <button
                onClick={() =>
                  setModalState({
                    ...modalState,
                    isOpen: true,
                    type: "create",
                    detailInfo: undefined,
                  })
                }
                className="btn w-full bg-primary border-none hover:bg-primary-hover"
              >
                <PlusOutlined style={{ fontSize: "18px", color: "white" }} />
                <span className="font-bold uppercase text-white ">
                  Add a user
                </span>
              </button>
            </div>
          )}
        />
      </div>
    </>
  );
};

export default TableTAccount;
