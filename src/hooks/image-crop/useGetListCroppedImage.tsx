import imageRequest from "@/modules/image/api/image.api";
import { useState } from "react";

const useGetListCroppedImage = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  async function request(params: {
    imageId?: number;
    skipCount?: number;
    maxResultCount?: number;
  }) {
    setLoading(true);
    const response = await imageRequest.getListCroppedImage(params);
    if (response?.state === "success") {
      // setData(
      //   response.data && response.data.result && response.data.result.items
      //     ? response.data.result.items
      //     : []
      // );
      setLoading(false);
    } else {
      setLoading(false);
      return null;
    }
  }
  return {
    request,
    data,
    loading,
  };
};

export default useGetListCroppedImage;
