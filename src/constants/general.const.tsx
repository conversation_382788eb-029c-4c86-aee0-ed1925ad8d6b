export const IMAGE_TYPE = {
  Original: {
    value: 1,
    label: "Original",
  },
  Cropped: {
    value: 2,
    label: "Cropped Box",
  },
  CroppedRow: {
    value: 3,
    label: "Cropped Row",
  },
  Segmented: {
    value: 4,
    label: "Segmented",
  },
  Mask: {
    value: 5,
    label: "Mask",
  },
};

export const Units = {
  Metres: {
    value: "Metres",
    label: "Metres",
  },
  Feet: {
    value: "Feet",
    label: "Feet",
  },
  Inches: {
    value: "Inches",
    label: "Inches",
  },
};

export enum RequestState {
  initial = "initial",
  request = "request",
  success = "success",
  error = "error",
}

export const MODEL_TYPE = {
  AutocropAndCoreOutline: {
    value: 8,
    label: "Autocrop and core outline",
  },
  Autocrop: {
    value: 11,
    label: "Autocrop",
  },
  // AutocropAndPiecesSAM: {
  //   value: 12,
  //   label: "Autocrop and Pieces SAM",
  // },
  AutocropAndPiecesRCNN: {
    value: 13,
    label: "Autocrop and Pieces RCNN",
  },
};

export const PROCESS_TYPE = {
  AiBase: {
    value: 1,
    label: "FastGeo Model",
  },
  Tool: {
    value: 3,
    label: "Tools",
  },
};

export const TOOL_TYPE = {
  Crop: {
    value: 1,
    label: "Crop",
  },
  RotateClockwise: {
    value: 2,
    label: "Rotate Clockwise",
  },
  RotateCounterClockwise: {
    value: 3,
    label: "Rotate Counterclockwise",
  },
  Align: {
    value: 4,
    label: "Align",
  },
  AssignRow: {
    value: 5,
    label: "Assign Row",
  },
  AssignBox: {
    value: 6,
    label: "Assign Box",
  },
};

export const DATA_TYPE = {
  Step: {
    value: 1,
    label: "Step",
  },
  Image: {
    value: 2,
    label: "Image",
  },
};

export enum OUTPUT_TYPE {
  Image = 1,
  Text,
  Coordinate,
}

export const PERMISSIONS = {
  Admin: {
    value: "Admin",
    label: "Admin",
  },
  EditorUser: {
    value: "EditorUser",
    label: "Editor",
  },
  ViewerUser: {
    value: "ViewerUser",
    label: "Viewer",
  },
  Company: {
    value: "Company",
    label: "Company",
  },
};

export const COLORS = [
  "rgba(255, 0, 0, 0.5)", // Red
  "rgba(0, 255, 0, 0.5)", // Green
  "rgba(0, 0, 255, 0.5)", // Blue
  "rgba(255, 255, 0, 0.5)", // Yellow
  "rgba(255, 0, 255, 0.5)", // Magenta
  "rgba(0, 255, 255, 0.5)", // Cyan
  "rgba(128, 0, 0, 0.5)", // Maroon
  "rgba(0, 128, 0, 0.5)", // Dark Green
  "rgba(0, 0, 128, 0.5)", // Navy
  "rgba(128, 128, 128, 0.5)", // Gray
  "rgba(255, 165, 0, 0.5)", // Orange
  "rgba(255, 105, 180, 0.5)", // Hot Pink
  "rgba(75, 0, 130, 0.5)", // Indigo
  "rgba(255, 192, 203, 0.5)", // Pink
  "rgba(192, 192, 192, 0.5)", // Silver
  "rgba(128, 0, 128, 0.5)", // Purple
  "rgba(255, 215, 0, 0.5)", // Gold
  "rgba(0, 128, 128, 0.5)", // Teal
  "rgba(0, 100, 0, 0.5)", // Dark Green
  "rgba(139, 69, 19, 0.5)", // Brown
];

export const IMAGESIZE = {
  width: 800,
  height: 600,
};
