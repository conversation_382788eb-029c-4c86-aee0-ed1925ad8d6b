import z from "zod";

export const AccountBody = z
  .object({
    id: z.any().optional(),
    userName: z.string().trim().min(1, { message: "Required" }),
    name: z.string().trim().min(1, { message: "Required" }).optional(),
    surname: z.string().trim().min(1, { message: "Required" }).optional(),
    fullName: z.string().trim().min(1, { message: "Required" }).optional(),
    emailAddress: z.string().email(),
    isActive: z.boolean().optional(),
    roleNames: z.any().optional(),
    password: z.string().optional(),
    lastLoginTime: z.any().optional(),
    creationTime: z.any().optional(),
    company: z.any().optional(),
    status: z.string().optional(),
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    roleConfigId: z.number().optional(),
    projectIds: z.array(z.number()).optional(),
    workRoleId: z.number().optional(),
  })
  .refine((data) => !(data.status === "create" && !data.password), {
    message: "Required",
    path: ["password"],
  });
export type AccountBodyType = z.TypeOf<typeof AccountBody>;

export const AccountSettingsBody = z.object({
  isSelectorArea: z.boolean().optional(),
  id: z.any().optional(),
  // productName: z.string().trim().min(1, { message: "Required" }),
  productName: z.string().trim().optional(),

  units: z.string().optional(),
  // unitsSymbol: z.string().trim().min(1, { message: "Required" }),
  unitsSymbol: z.string().trim().optional(),

  imageType: z.number().optional(),
  // collectionName: z.string().trim().min(1, { message: "Required" }),
  collectionName: z.string().trim().optional(),

  boundingPolygonType: z.number().optional(),
  rowPolygonType: z.number().optional(),
  viewWithBoundingPolygon: z.boolean().optional(),
  rowPolygon: z.boolean().optional(),
  logoProduct: z.string().optional(),
  tenantId: z.number().optional(),
  isUseLogo: z.boolean().optional(),
  imageStatus: z.any().optional(),
  projectId: z.number().optional(),
  prospectId: z.number().optional(),
  drillholeId: z.number().optional(),
  collectionNameSingular: z.string().optional(),
});

export type AccountSettingsBodyType = z.TypeOf<typeof AccountSettingsBody>;
