import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { AccountSettingsBodyType } from "@/modules/account/model/schema/account.schema";
import imageRequest from "@/modules/image/api/image.api";
import { InboxOutlined } from "@ant-design/icons";
import { Checkbox, Image, UploadProps } from "antd";
import Dragger from "antd/es/upload/Dragger";
import { useEffect, useState } from "react";
import { Control, UseFormGetValues, UseFormSetValue } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";
import { selectDetailAccountSettings } from "../redux/accountSettingsSlice";
interface IUploadLogoProps {
  control: Control<AccountSettingsBodyType>;
  setValue: UseFormSetValue<AccountSettingsBodyType>;
  getValue: UseFormGetValues<AccountSettingsBodyType>;
}

const UploadLogo = (props: IUploadLogoProps) => {
  const { control, setValue, getValue } = props;
  const url = getValue("logoProduct");
  const [imageUrl, setImageUrl] = useState("");
  const [isUseLogo, setIsUseLogo] = useState(getValue("isUseLogo"));
  const accountSettings = useAppSelector(selectDetailAccountSettings);

  useEffect(() => {
    if (url) {
      setImageUrl(url);
    }
  }, [url]);

  useEffect(() => {
    setIsUseLogo(getValue("isUseLogo"));
  }, [getValue("isUseLogo")]);

  const draggerProps: UploadProps = {
    name: "file",
    multiple: false,
    fileList: [],
    accept: ".jpg,.jpeg,.png,.gif",
    async onChange(info) {
      const formData = new FormData();
      formData.append("image", info.file.originFileObj as any);
      const uploadImage = await imageRequest.uploadImage({
        image: info.file.originFileObj,
      })
      setImageUrl(uploadImage?.data?.result);
      setValue("logoProduct", uploadImage?.data?.result);
    },
  };

  return (
    <div className="mb-6">
      <div className="flex gap-2">
        <div className="min-w-[230px]">
          <div className="flex items-center justify-center flex-col h-full">
            <div className="relative h-[46px] overflow-hidden flex items-center justify-center">
              {imageUrl && (
                <Image
                  src={imageUrl}
                  style={{
                    objectFit: "cover",
                  }}
                  width={230}
                  height={46}
                  alt="logo"
                />
              )}
            </div>
            <FormItem control={control} name="isSelectorArea">
              <Checkbox
                disabled={!accountSettings?.logoProduct || !imageUrl}
                checked={isUseLogo}
                onChange={(e) => {
                  setValue("isUseLogo", e.target.checked);
                  setIsUseLogo(e.target.checked);
                }}
                style={{ fontSize: "10px" }}
              >
                Use this image as the Account Logo
              </Checkbox>
            </FormItem>
          </div>
        </div>
        <div className="max-h-[200px] flex-1">
          <Dragger {...draggerProps} className="w-full">
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">
              Click or drag file to this area to change the Product Logo
            </p>
            <p className="ant-upload-hint">
              Use a file pixel size of 400 x 80.
            </p>
          </Dragger>
        </div>
      </div>
    </div>
  );
};

export default UploadLogo;
