import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { getErrorMessage } from "@/utils/error.utils";
import { GetAllTemplatesQuery } from "../interface/importData.query";

export enum ImportMode {
  Add = 1,
  Update = 2,
  AddAndUpdate = 3,
}
export enum ImportMappingType {
  Geology = 1,
  Geophysics = 2,
  Assay = 3,
  DownholeSurvey = 4,
  DrillHole = 5,
}
export const ImportMappingTypeOptions = [
  {
    label: "Drill Holes",
    value: ImportMappingType.DrillHole,
  },
  {
    label: "Assays",
    value: ImportMappingType.Assay,
  },
  {
    label: "Geology",
    value: ImportMappingType.Geology,
  },
  {
    label: "Geophysics",
    value: ImportMappingType.Geophysics,
  },

  {
    label: "Downhole Survey",
    value: ImportMappingType.DownholeSurvey,
  },
];
export const ImportModeOptions = [
  {
    label: "Add",
    value: ImportMode.Add,
  },
  {
    label: "Update",
    value: ImportMode.Update,
  },
  {
    label: "Add and Update",
    value: ImportMode.AddAndUpdate,
  },
];
export enum ImportFileType {
  Geology = 1,
  Geophysics = 2,
  Assay = 3,
  DownholeSurvey = 4,
  DrillHole = 5,
}

export const ImportFileTypeOptions = [
  {
    label: "Geology",
    value: ImportFileType.Geology,
  },
  {
    label: "Geophysics",
    value: ImportFileType.Geophysics,
  },
  {
    label: "Assay",
    value: ImportFileType.Assay,
  },
  {
    label: "Downhole Survey",
    value: ImportFileType.DownholeSurvey,
  },
  {
    label: "Drill Hole",
    value: ImportFileType.DrillHole,
  },
];

export interface IGetAllTemplatesRequest {
  Keyword: string;
  skipCount: number;
  maxResultCount: number;
}

export interface IImportFileRequest {
  projectId: number;
  prospectId: number;
  ImportMappingTemplateId: number;
  ImportMappingType: ImportMappingType;
  ImportMode: ImportMode;
  CreateNewDrillHoles: boolean;
  ExcelFile: File;
  ImportFileType?: ImportFileType;
  ImportMappingFields?: any;
  SuiteId?: number;
  DownholeSurveyTypeId?: number;
}

export interface IGetMappingFieldsRequest {
  importFileType: ImportFileType;
  suiteId?: any;
}
const importDataRequest = {
  importData: async (data: IImportFileRequest) => {
    try {
      const response = await appRequest.upload<any>(
        `/services/app/ImportData/ImportFile`,
        [
          {
            key: "projectId",
            value: data.projectId,
          },
          {
            key: "prospectId",
            value: data.prospectId,
          },
          {
            key: "ExcelFile",
            value: data.ExcelFile,
          },
          {
            key: "ImportMappingTemplateId",
            value: data.ImportMappingTemplateId,
          },
          {
            key: "ImportMappingType",
            value: data.ImportMappingType,
          },
          {
            key: "ImportMode",
            value: data.ImportMode,
          },
          {
            key: "CreateNewDrillHoles",
            value: data.CreateNewDrillHoles,
          },
          {
            key: "ImportFileType",
            value: data.ImportFileType,
          },
          {
            key: "ImportMappingFields",
            value: data.ImportMappingFields,
          },
          {
            key: "SuiteId",
            value: data.SuiteId,
          },
          {
            key: "DownholeSurveyTypeId",
            value: data.DownholeSurveyTypeId,
          },
        ]
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error: any) {
      return {
        state: RequestState.error,
        message: error?.result,
      };
    }
  },
  uploadGeology: async (body: {
    projectId: string;
    file: File;
    GeologySuiteId: string;
  }) => {
    try {
      const response = await appRequest.upload<any>(
        `/services/app/Downhole/UploadGeologyData`,
        [
          {
            key: "projectId",
            value: body.projectId,
          },
          {
            key: "ExcelFile",
            value: body.file,
          },
          {
            key: "GeologySuiteId",
            value: body.GeologySuiteId,
          },
        ]
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  previewMappingFields: async (data: IGetMappingFieldsRequest) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/ImportData/PreviewMappingFields`,
        data
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getImportTemplate: async ({ Id }: { Id: number }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/ImportData/GetImportTemplate?Id=${Id}`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getMapppingFields: async (data: any) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/ImportData/GetMappingFields`,
        data
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getAllTemplates: async (data: GetAllTemplatesQuery) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/ImportData/GetAllTemplates`,
        data
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  saveTemplate: async (data: any) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/ImportData/SaveTemplate`,
        data
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default importDataRequest;
