import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import http from "@/lib/http";
import { AccountSettingsBodyType } from "@/modules/account/model/schema/account.schema";
import { getErrorMessage } from "@/utils/error.utils";

const accountSettingsRequest = {
  getAllAccountSettings: async (params: {
    keyword?: string;
    skipCount?: number;
    maxResultCount?: number;
  }) => {
    try {
      return http.get(`/services/app/SettingAccount/GetAll`, { params });
    } catch (error) {
      return Promise.reject(error);
    }
  },
  getAccountSettings: async (params: { id?: string | number }) => {
    try {
      return http.get(`/services/app/SettingAccount/Get`, { params });
    } catch (error) {
      return Promise.reject(error);
    }
  },
  getDetailsAccountSettings: async () => {
    try {
      return http.get(`/services/app/SettingAccount/GetDetailSetting`);
    } catch (error) {
      return Promise.reject(error);
    }
  },
  updateAccountSettings: async (body: AccountSettingsBodyType) => {
    try {
      return http.put(`/services/app/SettingAccount/Update`, body);
    } catch (error) {
      return Promise.reject(error);
    }
  },
  deleteAccountSettings: async (params: { id: string | number }) => {
    try {
      return http.delete(`/services/app/SettingAccount/Delete`, { params });
    } catch (error) {
      return Promise.reject(error);
    }
  },
  importGeologyConfig: async (body: { file: File }) => {
    try {
      const response = await appRequest.upload<any>(
        `/services/app/SettingAccount/ImportGeologyConfig`,
        [
          {
            key: "File",
            value: body.file,
          },
        ]
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  exportGeologyConfig: async () => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/SettingAccount/ExportGeologyConfig`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default accountSettingsRequest;
