import { ModalCommon } from "@/components/common/modal-common";
import { InfoCircleOutlined } from "@ant-design/icons";
import { Button, Tag } from "antd";
import { useEffect, useState } from "react";
import drillholeRequest from "@/modules/drillhole/api/drillhole.api";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { TableCommon } from "@/components/common/table-common";

export default function InfoDrillhole() {
  const [open, setOpen] = useState(false);
  const [data, setdata] = useState<any>(null);
  const drillholeDetail = useAppSelector((state) => state.drillHole?.detail);

  useEffect(() => {
    const getTotalImageTypeByDrillHole = async () => {
      const response = await drillholeRequest.getTotalImageTypeByDrillHole({
        drillHoleId: drillholeDetail?.id,
      });
      setdata(response.data);
    };
    getTotalImageTypeByDrillHole();
  }, [drillholeDetail]);
  console.log(data);
  const columns = [
    {
      title: "Image Type",
      dataIndex: "imageType",
      render: (value, record, index) => {
        return <>{value?.name}</>;
      },
    },
    {
      title: "Total",
      dataIndex: "total",
    },
  ];

  return (
    <div>
      <Button icon={<InfoCircleOutlined />} onClick={() => setOpen(true)} />
      <ModalCommon
        open={open}
        onCancel={() => setOpen(false)}
        footer={false}
        title="Drillhole Info"
        centered
      >
        <div>
          <TableCommon pagination={false} columns={columns} dataSource={data} />
        </div>
      </ModalCommon>
    </div>
  );
}
