import { But<PERSON><PERSON><PERSON>mon } from "@/components/common/button-common";
import { ModalCommon } from "@/components/common/modal-common";
import userRequest from "@/modules/account/api/user.api";
import { useState } from "react";
import { toast } from "react-toastify";

export default function ModalSetAdmin({
  modalState,
  setModalState,
  fetchListUser,
}: {
  modalState: any;
  setModalState: any;
  fetchListUser: any;
}) {
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const [loading, setLoading] = useState(false);
  const isAdmin = modalState.detailInfo?.roleNames?.includes("Company");
  const handleConfirm = () => {
    setLoading(true);
    if (isAdmin) {
      userRequest
        .deleteAdmin({ id: modalState.detailInfo?.id })
        .then((res) => {
          toast.success("Delete admin successfully");
          fetchListUser();
          setModalState({ ...modalState, isOpen: false });
        })
        .catch((err) => {
          toast.error("Delete admin failed");
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      userRequest
        .setAdmin({ id: modalState.detailInfo?.id })
        .then((res) => {
          toast.success("Set admin successfully");
          fetchListUser();
          setModalState({ ...modalState, isOpen: false });
        })
        .catch((err) => {
          toast.error("Set admin failed");
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      <div className="flex flex-col gap-2">
        <p className="font-bold text-16-18 capitalize font-visby">
          Are you sure you want to set this user as admin?
        </p>
        <p>
          This action cannot be undone. This will permanently set the user as
          admin
        </p>
        <div className="flex justify-end gap-2">
          <ButtonCommon
            onClick={handleCancel}
            className="btn py-2  btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
          >
            No
          </ButtonCommon>
          <ButtonCommon
            loading={loading}
            onClick={handleConfirm}
            className="btn btn-sm hover:bg-primary-hover bg-primary text-white border-none"
          >
            Yes
          </ButtonCommon>
        </div>
      </div>
    </ModalCommon>
  );
}
