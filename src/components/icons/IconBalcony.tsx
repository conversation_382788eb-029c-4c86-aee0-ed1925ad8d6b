import React from "react";

const IconBalcony = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.32626 3.00045C3.14184 3.01893 3.00151 3.17432 3.00213 3.35976V5.9479C3.00133 6.14669 3.16184 6.30849 3.36072 6.30931H8.09626C8.29504 6.30851 8.45556 6.14669 8.45484 5.9479V3.35976C8.45444 3.16168 8.29423 3.00117 8.09626 3.00045H3.36072C3.34916 2.99985 3.33771 2.99985 3.32626 3.00045H3.32626ZM3.72213 3.72115H5.36743V5.58934H3.72213V3.72115ZM6.08743 3.72115H7.73484V5.58934H6.08743V3.72115ZM9.40406 5.74334C9.30803 5.74294 9.21593 5.78091 9.14812 5.84871C9.08022 5.91661 9.04225 6.00873 9.04265 6.10475V9.06215H3.36083C3.2652 9.06205 3.17339 9.09992 3.10579 9.16752C3.03809 9.23512 3.00012 9.32683 3.00012 9.42245C3.00012 9.51808 3.03809 9.60979 3.10579 9.67739C3.17339 9.74499 3.2652 9.78286 3.36083 9.78286H3.56544V14.8797C3.56503 14.9756 3.603 15.0677 3.6709 15.1356C3.73871 15.2034 3.83082 15.2414 3.92684 15.2411H14.8809C14.8928 15.2409 14.9048 15.2402 14.9167 15.2389C15.1017 15.2211 15.2427 15.0654 15.2423 14.8796V6.10458C15.2426 6.00855 15.2046 5.91644 15.1368 5.84854C15.0689 5.78074 14.9768 5.74277 14.8809 5.74317L9.40406 5.74334ZM9.76265 6.46334H14.5221V9.06203H9.76265V6.46334ZM10.4004 6.73193C10.3049 6.73354 10.214 6.77311 10.1477 6.84182C10.0814 6.91052 10.045 7.00273 10.0467 7.09826V7.61154C10.0389 7.71158 10.0732 7.81032 10.1413 7.88396C10.2094 7.95769 10.3053 7.99957 10.4056 7.99957C10.5059 7.99957 10.6018 7.95769 10.67 7.88396C10.7381 7.81033 10.7723 7.71159 10.7646 7.61154V7.09826C10.7663 7.00092 10.7285 6.90711 10.6599 6.838C10.5913 6.769 10.4977 6.73073 10.4004 6.73193L10.4004 6.73193ZM4.28527 9.78267H9.04267V14.521H4.28527V9.78267ZM9.76267 9.78267H14.5221V14.521H9.76267V9.78267ZM10.4004 10.0513C10.388 10.0513 10.3755 10.0521 10.3631 10.0534C10.1798 10.0758 10.0432 10.2331 10.0467 10.4176V10.9302C10.0389 11.0302 10.0733 11.1291 10.1414 11.2027C10.2095 11.2763 10.3053 11.3182 10.4056 11.3182C10.506 11.3182 10.6018 11.2763 10.67 11.2027C10.7381 11.1291 10.7723 11.0302 10.7646 10.9302V10.4175V10.4176C10.7663 10.3202 10.7286 10.2264 10.6599 10.1573C10.5913 10.0883 10.4977 10.0501 10.4004 10.0513L10.4004 10.0513ZM6.14856 10.1785V10.1784C6.05163 10.1763 5.95801 10.2133 5.88879 10.2811C5.81948 10.3488 5.78041 10.4416 5.78041 10.5384C5.78041 10.6354 5.81948 10.7282 5.88879 10.7959C5.958 10.8637 6.05161 10.9006 6.14856 10.8984H7.18214C7.27897 10.9006 7.37259 10.8637 7.44191 10.7959C7.51111 10.7282 7.55019 10.6354 7.55019 10.5384C7.55019 10.4416 7.51111 10.3488 7.44191 10.2811C7.3726 10.2133 7.27898 10.1763 7.18214 10.1784L6.14856 10.1785ZM4.90473 11.3C4.72051 11.3184 4.5804 11.4735 4.5806 11.6585V13.8805C4.58141 14.0781 4.74142 14.2382 4.93919 14.2389H8.39159C8.58927 14.2382 8.74938 14.0781 8.75017 13.8805V11.6586C8.74937 11.4608 8.58926 11.3007 8.39159 11.3H4.93919C4.92764 11.2994 4.91619 11.2994 4.90474 11.3H4.90473ZM5.3006 12.02H8.03017V13.5191L5.3006 13.519V12.02Z"
        fill="#808191"
      />
    </svg>
  );
};

export default IconBalcony;
