import { Switch } from "antd";
import { Controller } from "react-hook-form";

export interface IToogleCommonProps {
  control: any;
  name: string;
  label: string;
  isVertical?: boolean;
  defaultValue?: boolean;
  error?: string;
  disabled?: boolean;
}
export function ToogleCommon(props: IToogleCommonProps) {
  const { control, name, label, isVertical, defaultValue, error, disabled } =
    props;
  return (
    <div
      className={`flex gap-2 ${isVertical ? "flex-col justify-center" : ""} ${
        error ? "border border-red-300 rounded p-2 bg-red-50" : ""
      }`}
    >
      <p className={`font-medium ${error ? "text-red-600" : ""}`}>{label}</p>
      <div className="">
        <Controller
          control={control}
          name={name}
          render={({ field }) => (
            <Switch
              {...field}
              defaultChecked={defaultValue}
              checked={field.value}
              disabled={disabled}
              className={error ? "border-red-300" : ""}
            />
          )}
        />
      </div>
      {error && (
        <div className="text-red-500 text-xs mt-1 font-medium">{error}</div>
      )}
    </div>
  );
}
