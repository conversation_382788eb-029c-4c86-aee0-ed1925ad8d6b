import accountSettingsRequest from "@/modules/account/api/account.api";
import { AccountSettingsBodyType } from "@/modules/account/model/schema/account.schema";

const useUpdateAccountSettings = () => {
  async function request(
    params: AccountSettingsBodyType,
    setLoading: Function,
    onSuccess: Function,
    onError: Function
  ) {
    setLoading(true);
    const response = await accountSettingsRequest.updateAccountSettings(params);
    if (response?.status === 200) {
      onSuccess(response.data);
      setLoading(false);
    } else {
      onError(response);
      setLoading(false);
    }
  }
  return [request];
};

export default useUpdateAccountSettings;
