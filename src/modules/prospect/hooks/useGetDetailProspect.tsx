import { useState } from "react";
import prospectRequest from "../api/prospect.api";

export const useGetDetailProspect = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);

  async function request(id: string, onSuccess?: Function, onError?: Function) {
    setLoading(true);
    const response = await prospectRequest.getDetail(id);
    if (response?.state === "success") {
      setData(response.data);
      setLoading(false);
      onSuccess && onSuccess(response.data);
      return response.data;
    } else {
      setLoading(false);
      onError && onError(response.message);
      return null;
    }
  }

  return { request, loading, data };
};
