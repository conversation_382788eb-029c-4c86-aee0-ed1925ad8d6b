import { Button<PERSON><PERSON>mon } from "@/components/common/button-common";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useCreateUser } from "@/modules/account-management/hooks/useCreateUser.hook";
import { useDeleteUser } from "@/modules/account-management/hooks/useDeleteUser.hook";
import { useUpdateUser } from "@/modules/account-management/hooks/useUpdateUser.hook";
import { AccountBodyType } from "@/modules/account/model/schema/account.schema";
import { useGetListProject } from "@/modules/projects/hooks/useGetListProject";
import { useGetListUserRoleConfig } from "@/modules/user-role-config/hooks/useGetListUserRoleConfig";
import { useGetListWorkRole } from "@/modules/work-role/hooks/useGetListWorkRole";
import { Form, Spin } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import userRequest from "../api/user.api";

export interface IModalCompanyProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  fetchListUser: () => void;
  isSystemAdmin: boolean;
}

export function ModalUser({
  modalState,
  setModalState,
  fetchListUser,
}: IModalCompanyProps) {
  const id = modalState.detailInfo?.id;

  const [requestCreateUser] = useCreateUser();
  const [requestUpdateUser] = useUpdateUser();
  const [requestDeleteUser] = useDeleteUser();
  const [loading, setLoading] = useState(false);
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const isSystemAdminRole =
    modalState.detailInfo?.roleNames[0] === "SystemAdmin";
  const { control, handleSubmit, setValue } = useForm<AccountBodyType>({
    // resolver: zodResolver(AccountBody),
    defaultValues: {
      emailAddress: modalState?.detailInfo?.emailAddress,
      userName: modalState?.detailInfo?.emailAddress,
      isActive: modalState?.detailInfo?.isActive ?? true,
      roleNames: modalState?.detailInfo?.roleNames,
      company: modalState?.detailInfo?.company,
      status: modalState?.type,
      firstName: modalState?.detailInfo?.firstName,
      lastName: modalState?.detailInfo?.lastName,
      roleConfigId: modalState?.detailInfo?.roleConfigId,
      projectIds: modalState?.detailInfo?.projectIds,
      workRoleId: modalState?.detailInfo?.workRoleId,
    },
  });
  const isConfirm = modalState.type === "delete";

  const onSubmit = (values: AccountBodyType) => {
    if (modalState.type === "create") {
      requestCreateUser(
        {
          ...values,
          userName: values.emailAddress,
        },
        setLoading,
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Create user successfully");
          fetchListUser();
        },
        () => {}
      );
    }
    if (modalState.type === "update") {
      requestUpdateUser(
        {
          id: modalState.detailInfo.id,
          ...values,
          userName: values.emailAddress,
        },
        setLoading,
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update user successfully", {
            autoClose: 200,
            position: "top-center",
          });
          fetchListUser();
        },
        () => {}
      );
    }
  };

  const handleDelete = () => {
    requestDeleteUser(
      {
        id: modalState.detailInfo.id,
      },
      setLoading,
      () => {
        setModalState({ ...modalState, isOpen: false });
        fetchListUser();
      },
      () => {}
    );
  };
  const {
    data: listUserRoleConfig,
    loading: loadingUserRoleConfig,
    request: requestGetListUserRoleConfig,
    maxResultCount: maxResultCountUserRoleConfig,
    handleScroll: handleScrollUserRoleConfig,
    keyword: keywordUserRoleConfig,
    setKeyword: setKeywordUserRoleConfig,
  } = useGetListUserRoleConfig();
  console.log(keywordUserRoleConfig);
  const {
    data: listProject,
    loading: loadingProject,
    request: requestGetListProject,
  } = useGetListProject();
  const [maxResultCountProject, setMaxResultCountProject] = useState(10);
  const handleScrollProject = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProject(maxResultCountProject + 10);
    }
  };
  const [keywordProject, setKeywordProject] = useState("");
  useEffect(() => {
    requestGetListUserRoleConfig({
      maxResultCount: maxResultCountUserRoleConfig,
      skipCount: 0,
      keyword: keywordUserRoleConfig,
      isActive: true,
    });
  }, [maxResultCountUserRoleConfig, keywordUserRoleConfig]);
  useEffect(() => {
    requestGetListProject({
      maxResultCount: maxResultCountProject,
      skipCount: 0,
      keyword: keywordProject,
      isActive: true,
    });
  }, [keywordProject, maxResultCountProject]);
  useEffect(() => {
    if (id) {
      userRequest.getUserById(id).then((res) => {
        const result = res?.data?.result;
        setValue("roleConfigId", result?.userRoleConfig?.id);
        setValue(
          "projectIds",
          result?.projects?.map((project) => project.id)
        );
        setValue("workRoleId", result?.workRole?.id);
      });
    }
  }, [id]);
  const {
    data: listWorkRole,
    request: requestGetListWorkRole,
    loading: loadingWorkRole,
  } = useGetListWorkRole();
  const [keywordWorkRole, setKeywordWorkRole] = useState("");
  useEffect(() => {
    requestGetListWorkRole({
      maxResultCount: 1000,
      skipCount: 0,
      keyword: keywordWorkRole,
    });
  }, [keywordWorkRole]);
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this user?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the user
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loading}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === "update" ? "Update a user" : "Create a user"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit)}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="First Name"
              name="firstName"
              placeholder="Enter first name"
              control={control}
            />
            <InputTextCommon
              label="Last Name"
              name="lastName"
              placeholder="Enter last name"
              control={control}
            />
            <InputTextCommon
              label="Email"
              name="emailAddress"
              placeholder="Enter your email"
              control={control}
              disabled={modalState.type === "update"}
            />
            {/* <InputTextCommon
              label="Username"
              name="userName"
              placeholder="Enter username"
              control={control}
              disabled={modalState.type === "update"}
            /> */}

            {/* {!isCompanyRole && (
              <SelectCommon
                name="company"
                label="Company"
                control={control}
                placeholder="Select company"
                disabled={Boolean(modalState.detailInfo)}
                options={listTenant.map((x) => {
                  return {
                    label: x.tenancyName,
                    value: x.id,
                  };
                })}
              />
            )} */}
            {!isSystemAdminRole && (
              <>
                <SelectCommon
                  onPopupScroll={handleScrollUserRoleConfig}
                  name="roleConfigId"
                  label="User Role"
                  control={control}
                  placeholder="Select User Role"
                  showSearch
                  filterOption={false}
                  searchValue={keywordUserRoleConfig}
                  onSearch={(value) => setKeywordUserRoleConfig(value)}
                  onBlur={() => setKeywordUserRoleConfig("")}
                  allowClear
                  options={listUserRoleConfig?.map((x) => ({
                    label: x.name,
                    value: x.id,
                  }))}
                  notFoundContent={
                    loadingUserRoleConfig ? (
                      <Spin size="small" />
                    ) : (
                      <>Not found</>
                    )
                  }
                />
                <SelectCommon
                  name="projectIds"
                  label="Project"
                  control={control}
                  placeholder="Select Project"
                  showSearch
                  onPopupScroll={handleScrollProject}
                  filterOption={false}
                  searchValue={keywordProject}
                  onSearch={(value) => setKeywordProject(value)}
                  onBlur={() => setKeywordProject("")}
                  allowClear
                  options={listProject?.map((x) => ({
                    label: x.name,
                    value: x.id,
                  }))}
                  mode="multiple"
                  notFoundContent={
                    loadingProject ? <Spin size="small" /> : <>Not found</>
                  }
                />
                <SelectCommon
                  name="workRoleId"
                  label="Work Role"
                  control={control}
                  placeholder="Select Work Role"
                  showSearch
                  filterOption={false}
                  searchValue={keywordWorkRole}
                  onSearch={(value) => setKeywordWorkRole(value)}
                  onBlur={() => setKeywordWorkRole("")}
                  allowClear
                  options={listWorkRole?.map((x) => ({
                    label: x.name,
                    value: x.id,
                  }))}
                  notFoundContent={
                    loadingWorkRole ? <Spin size="small" /> : <>Not found</>
                  }
                />
              </>
            )}

            <ToogleCommon label="Active" control={control} name="isActive" />
            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                loading={loading}
                type="submit"
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === "update" ? "Update a user" : "Add a user"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
