import {
  HubConnection,
  HubConnectionBuilder,
  HubConnectionState,
} from "@microsoft/signalr";

let signalRConnection: HubConnection | undefined = undefined;

export const getSignalRConnection = (pathName = "signalr-WorkflowJob") => {
  if (!signalRConnection) {
    signalRConnection = new HubConnectionBuilder()
      .withUrl(`${process.env.NEXT_PUBLIC_PRIMARY_ENDPOINT_SOCKET}/${pathName}`)
      .build();
  }

  //   if (signalRConnection.state === HubConnectionState.Disconnected)
  // signalRConnection.start();
  return signalRConnection;
};
