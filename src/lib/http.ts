import { appContants } from "@/common/configs";
import { appStorage } from "@/common/configs/app.di-container";
import Constants from "@/constants/constant";
import axios, {
  AxiosError,
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";
import Cookies from "js-cookie";
import { toast } from "react-toastify";

const onRequest = (
  config: InternalAxiosRequestConfig | any
): InternalAxiosRequestConfig => {
  // Set Headers Here/ Check Authentication Here
  const accessToken = appStorage.getItem(appContants.tokenKey);
  config.headers = {
    ...config.headers,
    authorization: accessToken ? `Bearer ${accessToken}` : null,
  };
  return config;
};

const onResponse = (response: AxiosResponse): AxiosResponse => {
  if (response.config.url === "/services/app/Account/Register") {
    toast.success("Register success");
    window.location.href = "/mail-success";
  }
  return response;
};

const onErrorResponse = async (
  error: AxiosError | Error | any
): Promise<AxiosError> => {
  if (axios.isAxiosError(error)) {
    const { status } = (error.response as AxiosResponse) ?? {};

    switch (status) {
      case 401: {
        setTimeout(() => {
          Cookies.remove("accessToken");
          window.location.href = "/login";
        }, 3000);
        break;
      }
      default: {
        break;
      }
    }
  } else {
  }

  return Promise.reject(error);
};

export class Http {
  instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: process.env.NEXT_PUBLIC_PRIMARY_ENDPOINT || Constants.API_URL,
      // timeout: Constants.TIMEOUT,
      headers: {
        "Content-Type": "application/json",
      },
    });
    this.instance.interceptors.request.use(onRequest, onErrorResponse);
    this.instance.interceptors.response.use(onResponse, (error) => {
      onErrorResponse(error).then((r) => {});
      toast.error(error?.response?.data?.error?.message || "Error");
    });
  }
}

export class HttpImage {
  instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: process.env.NEXT_PUBLIC_PRIMARY_ENDPOINT || Constants.API_URL,
      timeout: Constants.TIMEOUT,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    this.instance.interceptors.request.use(onRequest, onErrorResponse);
    this.instance.interceptors.response.use(onResponse, onErrorResponse);
  }
}

const http = new Http().instance;
export const httpImage = new HttpImage().instance;
export default http;
