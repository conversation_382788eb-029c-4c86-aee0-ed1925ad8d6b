import userRequest from "@/modules/account/api/user.api";
import { AccountBodyType } from "@/modules/account/model/schema/account.schema";

export const useUpdateUser = () => {
  async function request(
    params: AccountBodyType,
    setLoading: Function,
    onSuccess: Function,
    onError: Function
  ) {
    try {
      setLoading(true);
      const response = await userRequest.updateUser({
        ...params,
        isActive: params.isActive === true ? true : false,
      });
      if (response.status === 200) {
        onSuccess(response.data);
        setLoading(false);
      }
    } catch (error) {
      onError(error);
      setLoading(false);
    }
  }
  return [request];
};
