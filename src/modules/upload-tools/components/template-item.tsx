import { Button, Input, Select, Switch } from "antd";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useSaveTemplate } from "../hooks/useSaveTemplate";
import { setField } from "../redux/importDataSlice/import-data.slice";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";

export const TemplateItem = ({
  data,
  refetchTemplate,
  setValue,
  onSaveTemplate,
  suiteId,
  setOpenModalMapping,
  watch,
}: {
  data: any;
  refetchTemplate: () => void;
  setValue: any;
  onSaveTemplate: () => void;
  suiteId: any;
  setOpenModalMapping: (open: boolean) => void;
  watch: any;
}) => {
  const dispatch = useAppDispatch();
  const { header, field } = useAppSelector((state) => state.importData);
  const { request: requestSaveTemplate, loading: loadingSaveTemplate } =
    useSaveTemplate();

  const [isCreateTemplate, setIsCreateTemplate] = useState(false);

  const handleSaveTemplate = () => {
    if (!templateName && isCreateTemplate) {
      toast.error("Template Name is required");
      return;
    }

    if (isCreateTemplate) {
      const payload = {
        name: templateName,
        fields: field,
        importFileType:
          data?.type === "GeophysicsSuite"
            ? 2
            : data?.type === "AssaySuite"
            ? 3
            : data?.type === "DownholeSurvey"
            ? 4
            : data?.type === "DrillHole" || data?.type === "Drillhole"
            ? 5
            : 1,
        suiteId,
      };

      requestSaveTemplate(payload, (res) => {
        setValue("ImportMappingTemplateId", res.id);
        toast.success("Template saved successfully");
        onSaveTemplate();
        refetchTemplate();
      });
    } else {
      setValue("ImportMappingFields", JSON.stringify(field));
      setValue("suiteId", suiteId);
      setOpenModalMapping(false);
    }
  };
  const [templateName, setTemplateName] = useState<string>("");

  useEffect(() => {
    dispatch(setField([]));
  }, [suiteId]);

  useEffect(() => {
    let result: any[] = [];
    if (data?.matchedHeaders?.length) {
      data?.matchedHeaders?.forEach((d) => {
        const index = header.findIndex((_d) => _d === d);
        if (index !== -1) {
          result.push({
            systemFieldName: d,
            fileColumnName: header[index],
            sequence: index,
          });
        }
      });
    }
    if (data?.matchedAttributes?.length) {
      data?.matchedAttributes?.forEach((d) => {
        console.log(d);

        const index = header.findIndex((_d) => _d === d);
        if (index !== -1) {
          result.push({
            systemFieldName: d,
            fileColumnName: header[index],
            sequence: index,
          });
        }
      });
    }
    dispatch(setField(result));
  }, [header, data, watch("ImportFileType")]);

  useEffect(() => {
    const _field = field.map((d) => {
      return {
        ...d,
        systemFieldName: d.systemFieldName.trim(),
        fileColumnName: d.fileColumnName.trim(),
      };
    });
    setValue("ImportMappingFields", JSON.stringify(_field));
  }, [field]);

  return (
    <div className="flex flex-col gap-2 p-2 border rounded-md border-black">
      <div className="flex flex-col gap-2">
        <div className="flex gap-2">
          <p>Create Template</p>
          <Switch
            checked={isCreateTemplate}
            onChange={(checked) => setIsCreateTemplate(checked)}
          />
        </div>

        {isCreateTemplate && (
          <div className="flex gap-2">
            <p className="font-bold min-w-12">Name*</p>
            <Input
              placeholder="Template Name"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
            />
          </div>
        )}
        <div className="flex flex-col border rounded-md p-2 gap-2 max-h-[300px] overflow-y-auto">
          {data?.matchedAttributes?.map((d, index) => (
            <div className="grid grid-cols-2 gap-2" key={index}>
              <p className="font-bold">{d}</p>
              <Select
                key={JSON.stringify(suiteId)}
                options={header.map((d) => ({
                  label: d,
                  value: d,
                }))}
                showSearch
                filterOption={true}
                optionFilterProp="label"
                placeholder="Select a header"
                onChange={(value) => {
                  let _field = [...field];
                  const isExist = _field.find((_d) => _d.systemFieldName === d);
                  if (isExist) {
                    _field = _field.filter((_d) => _d.systemFieldName !== d);
                  }
                  _field.push({
                    systemFieldName: d,
                    fileColumnName: value,
                  });
                  dispatch(setField(_field));
                }}
                value={
                  field.find((_d) => _d.systemFieldName === d)?.fileColumnName
                }
              />
            </div>
          ))}
          {data?.matchedHeaders?.map((d, index) => (
            <div className="grid grid-cols-2 gap-2" key={index}>
              <p className="font-bold">{d}</p>
              <Select
                options={header.map((d) => ({
                  label: d,
                  value: d,
                }))}
                placeholder="Select a header"
                onChange={(value) => {
                  let _field = [...field];
                  const isExist = _field.find((_d) => _d.systemFieldName === d);
                  if (isExist) {
                    _field = _field.filter((_d) => _d.systemFieldName !== d);
                  }
                  _field.push({
                    systemFieldName: d,
                    fileColumnName: value,
                  });
                  dispatch(setField(_field));
                }}
                value={
                  field.find((_d) => _d.systemFieldName === d)?.fileColumnName
                }
              />
            </div>
          ))}
        </div>
      </div>
      <Button
        type="primary"
        onClick={handleSaveTemplate}
        loading={loadingSaveTemplate}
      >
        Save Template
      </Button>
    </div>
  );
};
