import { appContants } from "@/common/configs";
import { appStorage } from "@/common/configs/app.di-container";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { clearUserData } from "@/modules/auth/redux/userSlice";
import { clearLoggingData } from "@/modules/logging/redux/loggingSlice/logging.slice";
import { EditOutlined, LogoutOutlined } from "@ant-design/icons";
import { Dropdown, Space } from "antd";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { FaUser } from "react-icons/fa6";
import { GiFactory } from "react-icons/gi";
import { VscAccount } from "react-icons/vsc";
import { ModalChangeFontSize } from "./modal-change-fontsize";
export function UserHeader() {
  const user = useAppSelector((state) => state.user.userInfo);
  const router = useRouter();
  const dispatch = useAppDispatch();

  const logout = async () => {
    dispatch(clearUserData());
    dispatch(clearLoggingData());
    appStorage.removeItem(appContants.tokenKey);
    appStorage.removeItem(appContants.refreshToken);
    router.push("/login");
    window.location.pathname = "/login";
  };

  const [open, setOpen] = useState(false);
  return (
    <div className="flex gap-2 items-center">
      <ModalChangeFontSize open={open} setOpen={setOpen} />
      <Dropdown
        menu={{
          items: [
            {
              key: "2",
              label: "Change Password",
              icon: <EditOutlined />,
              onClick: () => {
                router.push("/change-password");
              },
            },
            {
              type: "divider",
            },
            {
              key: "3",
              label: "Log out",
              icon: <LogoutOutlined />,
              onClick: () => {
                logout();
              },
            },
            {
              type: "divider",
            },
            {
              key: "4",
              label: user?.firstName + " " + user?.lastName,
              icon: <FaUser />,
              onClick: () => {},
            },
            {
              type: "divider",
            },
            {
              key: "1",
              label: user?.companyName,
              icon: <GiFactory />,
              onClick: () => {},
            },
            // {
            //   key: "5",
            //   label: "Font Size",
            //   icon: <FontSizeOutlined />,
            //   onClick: () => setOpen(true),
            // },
          ],
        }}
      >
        <Space className="cursor-pointer">
          <VscAccount
            style={{
              width: 30,
              height: 30,
            }}
          />
        </Space>
      </Dropdown>
    </div>
  );
}
