import userRequest from "@/modules/account/api/user.api";

export const useGetListUserSystemAdmin = () => {
  async function request(
    params: {
      keyword?: string;
      isActive?: boolean;
      skipCount: number;
      maxResultCount: number;
    },
    setLoading: Function,
    onSuccess: Function,
    onError: Function
  ) {
    setLoading(true);
    const response = await userRequest.getListUserSystemAdmin(params);
    if (response?.status === 200) {
      onSuccess(response.data);
      setLoading(false);
    } else {
      onError(response);
      setLoading(false);
    }
  }

  return [request];
};
