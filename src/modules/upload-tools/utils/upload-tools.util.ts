import { ImportFileType } from "../api/importData.api";

export const getImportFileTypeLabel = (importFileType: ImportFileType): string => {
  switch (importFileType) {
    case ImportFileType.Geology:
      return "Geology";
    case ImportFileType.Geophysics:
      return "Geophysics";
    case ImportFileType.Assay:
      return "Assay";
    case ImportFileType.DownholeSurvey:
      return "Downhole Survey";
    case ImportFileType.DrillHole:
      return "Drill Hole";
    default:
      return "Unknown";
  }
};
