import { Button<PERSON><PERSON>mon } from "@/components/common/button-common";
import { SelectCommon } from "@/components/common/select-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useQueryProject } from "@/modules/projects/hooks/useQueryProject";
import { InfoCircleOutlined } from "@ant-design/icons";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, Divider, Form, Radio, Spin, Tooltip } from "antd";
import { useEffect, useMemo, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { toast } from "react-toastify";
import mobileProfileRequest from "../api/mobile-profile.api";
import { useUpdateMobileProfile } from "../hooks/useUpdateMobileProfile";
import { MobileCameraType } from "../interface/enum";
import {
  MobileProfileBody,
  MobileProfileBodyType,
} from "../model/schema/mobile-profile.schema";

export function MobileCog({
  mobileProfile,
  setModalState,
  refresh,
}: {
  mobileProfile: MobileProfileBodyType;
  setModalState: (value: any) => void;
  refresh: () => void;
}) {
  const [dataMobileProfile, setDataMobileProfile] =
    useState<MobileProfileBodyType>(mobileProfile);
  useEffect(() => {
    if (mobileProfile.id) {
      mobileProfileRequest
        .getMobileProfile({
          Id: mobileProfile.id,
        })
        .then((res) => {
          setDataMobileProfile(res.data);
          const projectIds = res?.data?.projects?.map((item: any) => item.id);
          setValue("projectIds", projectIds);
        });
    }
  }, [mobileProfile]);

  const {
    request: requestUpdateMobileProfile,
    loading: loadingUpdateMobileProfile,
  } = useUpdateMobileProfile();
  const { control, handleSubmit, setValue, getValues, watch, formState } =
    useForm<MobileProfileBodyType>({
      resolver: zodResolver(MobileProfileBody),
      defaultValues: {
        ...mobileProfile,
        isStandard: mobileProfile.isStandard ?? false,
        isRig: mobileProfile.isRig ?? false,
        isWet: mobileProfile.isWet ?? false,
        isDry: mobileProfile.isDry ?? false,
        isUv: mobileProfile.isUv ?? false,
        mobileCameraType:
          mobileProfile.mobileCameraType ?? MobileCameraType.STANDARD,
        externalCameraType:
          mobileProfile.externalCameraType ?? MobileCameraType.STANDARD,
        rotateImg: mobileProfile.rotateImg ?? 0,
        isDepthIncrement: mobileProfile.isDepthIncrement ?? false,
        isApplyDepthIncrement: mobileProfile.isApplyDepthIncrement ?? false,
      },
    });

  // Watch các field để thực hiện auto-toggle
  const isStandard = watch("isStandard");
  const isRig = watch("isRig");
  const isWet = watch("isWet");
  const isDry = watch("isDry");
  const isUv = watch("isUv");

  // Tạo dynamic options cho mobileCameraType và externalCameraType
  const dynamicCameraOptions = useMemo(() => {
    const options: { label: string; value: MobileCameraType }[] = [];

    if (isStandard) {
      options.push({
        label: "Standard",
        value: MobileCameraType.STANDARD,
      });
    }

    if (isRig) {
      options.push({
        label: "Rig",
        value: MobileCameraType.RIG,
      });
    }

    return options;
  }, [isStandard, isRig]);

  // Auto-toggle logic khi Standard/Rig thay đổi
  useEffect(() => {
    const currentValues = getValues();

    // Khi tắt Standard thì tắt hết subtype (Wet/Dry/UV)
    if (!isStandard) {
      setValue("isWet", false);
      setValue("isDry", false);
      setValue("isUv", false);
    }

    // Khi tắt cả Standard và Rig thì tắt Mobile Camera Settings
    if (!isStandard && !isRig) {
      setValue("mobileCameraType", undefined);
      setValue("externalCameraType", undefined);
    }

    // Ngăn chặn việc tắt cả Standard và Rig
    // Nếu user tắt Standard và Rig đang tắt, thì tự động bật Rig
    // Đảm bảo luôn có ít nhất 1 option được bật
    if (!isStandard && !isRig) {
      setValue("isRig", true);
    }

    // Khi tắt Standard thì tự động chuyển sang Rig
    if (!isStandard && isRig) {
      // Nếu mobileCameraType đang là Standard thì chuyển sang Rig
      if (getValues("mobileCameraType") === MobileCameraType.STANDARD) {
        setValue("mobileCameraType", MobileCameraType.RIG);
      }
      // Nếu externalCameraType đang là Standard thì chuyển sang Rig
      if (getValues("externalCameraType") === MobileCameraType.STANDARD) {
        setValue("externalCameraType", MobileCameraType.RIG);
      }
    }

    // Khi tắt Rig thì tự động chuyển sang Standard
    if (!isRig && isStandard) {
      // Nếu mobileCameraType đang là Rig thì chuyển sang Standard
      if (getValues("mobileCameraType") === MobileCameraType.RIG) {
        setValue("mobileCameraType", MobileCameraType.STANDARD);
      }
      // Nếu externalCameraType đang là Rig thì chuyển sang Standard
      if (getValues("externalCameraType") === MobileCameraType.RIG) {
        setValue("externalCameraType", MobileCameraType.STANDARD);
      }
    }

    // Đảm bảo luôn có giá trị cho camera types
    const currentMobileCameraType = getValues("mobileCameraType");
    const currentExternalCameraType = getValues("externalCameraType");

    // Nếu mobileCameraType chưa có giá trị, set giá trị mặc định
    if (!currentMobileCameraType) {
      if (isStandard) {
        setValue("mobileCameraType", MobileCameraType.STANDARD);
      } else if (isRig) {
        setValue("mobileCameraType", MobileCameraType.RIG);
      }
    }

    // Nếu externalCameraType chưa có giá trị, set giá trị mặc định
    if (!currentExternalCameraType) {
      if (isStandard) {
        setValue("externalCameraType", MobileCameraType.STANDARD);
      } else if (isRig) {
        setValue("externalCameraType", MobileCameraType.RIG);
      }
    }

    // Reset camera type values khi options thay đổi
    const availableValues = dynamicCameraOptions.map((option) => option.value);

    if (
      currentMobileCameraType &&
      !availableValues.includes(currentMobileCameraType)
    ) {
      // Nếu giá trị hiện tại không có trong available options, set giá trị mặc định
      if (isStandard) {
        setValue("mobileCameraType", MobileCameraType.STANDARD);
      } else if (isRig) {
        setValue("mobileCameraType", MobileCameraType.RIG);
      }
    }

    if (
      currentExternalCameraType &&
      !availableValues.includes(currentExternalCameraType)
    ) {
      // Nếu giá trị hiện tại không có trong available options, set giá trị mặc định
      if (isStandard) {
        setValue("externalCameraType", MobileCameraType.STANDARD);
      } else if (isRig) {
        setValue("externalCameraType", MobileCameraType.RIG);
      }
    }
  }, [isStandard, isRig, setValue, getValues, dynamicCameraOptions]);

  const onSubmit = (values: MobileProfileBodyType) => {
    requestUpdateMobileProfile(values, () => {
      toast.success("Update mobile profile successfully");
      setModalState((prev: any) => ({ ...prev, isOpen: false }));
      refresh();
    });
  };
  const handleCancel = () => {
    setModalState((prev: any) => ({ ...prev, isOpen: false }));
  };
  const {
    data: projects,
    setEnable: setEnableProject,
    isLoading: isLoadingProject,
    handleScroll: handleScrollProject,
    searchParams: searchParamsProject,
    setSearchParams: setSearchParamsProject,
  } = useQueryProject();
  useEffect(() => {
    setEnableProject(true);
  }, []);

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          Mobile Profile Configuration
        </h2>
        <p className="text-gray-600 text-sm">
          Configure your mobile profile settings for optimal performance
        </p>
        <Tooltip title="Note: You must configure any Image Types that you wish to use in Settings - Data Config - Image Types. Link Profiles to Projects.">
          <InfoCircleOutlined className="text-yellow-500 ml-2" />
        </Tooltip>
      </div>

      <Form
        onFinish={handleSubmit(onSubmit, (error) => {
          console.log(error);
        })}
        className="space-y-4"
      >
        {/* Project Selection */}
        <Card title="Project Assignment" size="small" className="shadow-sm">
          <SelectCommon
            onSearch={(value) => {
              setSearchParamsProject({
                ...searchParamsProject,
                keyword: value,
              });
            }}
            searchValue={searchParamsProject?.keyword}
            onBlur={() => {
              setSearchParamsProject({
                ...searchParamsProject,
                keyword: "",
              });
            }}
            allowClear
            options={projects?.data?.items.map((project) => ({
              label: project.name,
              value: project.id,
            }))}
            mode="multiple"
            notFoundContent={
              isLoadingProject ? <Spin size="small" /> : <>Not found</>
            }
            filterOption={false}
            showSearch
            placeholder="Select projects to assign"
            onPopupScroll={(event: any) => {
              handleScrollProject(event);
            }}
            control={control}
            name="projectIds"
            label="Projects"
          />
        </Card>

        {/* Camera Type Configuration */}
        <Card
          title="Camera Type Configuration"
          size="small"
          className="shadow-sm"
        >
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="space-y-2">
              <ToogleCommon
                control={control}
                name="isStandard"
                label="Standard Camera"
                error={formState.errors.isStandard?.message}
              />
              <div className="ml-6 space-y-2">
                <div className="flex gap-4">
                  <ToogleCommon
                    control={control}
                    name="isWet"
                    label="Wet"
                    disabled={!isStandard}
                    error={formState.errors.isWet?.message}
                  />
                  <ToogleCommon
                    control={control}
                    name="isDry"
                    label="Dry"
                    disabled={!isStandard}
                  />
                  <ToogleCommon
                    control={control}
                    name="isUv"
                    label="UV"
                    disabled={!isStandard}
                  />
                </div>
              </div>
            </div>
            <div>
              <ToogleCommon
                control={control}
                name="isRig"
                label="Rig Camera"
                error={formState.errors.isRig?.message}
              />
            </div>
          </div>

          <Divider />

          <div className="grid grid-cols-2 gap-4">
            <SelectCommon
              control={control}
              name="mobileCameraType"
              options={dynamicCameraOptions}
              defaultValue={MobileCameraType.STANDARD}
              label="Mobile Camera Type"
              placeholder="Select mobile camera type"
              disabled={!isStandard && !isRig}
            />
            <SelectCommon
              control={control}
              name="externalCameraType"
              options={dynamicCameraOptions}
              defaultValue={MobileCameraType.STANDARD}
              label="External Camera Type"
              placeholder="Select external camera type"
              disabled={!isStandard && !isRig}
            />
          </div>
        </Card>

        {/* Image Settings */}
        <Card title="Image Settings" size="small" className="shadow-sm">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Image Rotation
              </label>
              <Controller
                name="rotateImg"
                control={control}
                render={({ field }) => (
                  <Radio.Group
                    {...field}
                    className="flex gap-4"
                    value={field.value || 0}
                  >
                    <Radio value={0} className="flex items-center">
                      <span className="ml-2">0°</span>
                    </Radio>
                    <Radio value={90} className="flex items-center">
                      <span className="ml-2">90°</span>
                    </Radio>
                    <Radio value={180} className="flex items-center">
                      <span className="ml-2">180°</span>
                    </Radio>
                    <Radio value={270} className="flex items-center">
                      <span className="ml-2">270°</span>
                    </Radio>
                  </Radio.Group>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <ToogleCommon
                control={control}
                name="isDepthIncrement"
                label="Depth Increment"
                error={formState.errors.isDepthIncrement?.message}
              />
              <ToogleCommon
                control={control}
                name="isApplyDepthIncrement"
                label="Apply Depth Increment"
                error={formState.errors.isApplyDepthIncrement?.message}
              />
            </div>
          </div>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3 pt-4 border-t">
          <ButtonCommon
            loading={loadingUpdateMobileProfile}
            onClick={handleSubmit(onSubmit)}
            className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
          >
            Save Changes
          </ButtonCommon>
          <ButtonCommon
            onClick={handleCancel}
            className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
          >
            Cancel
          </ButtonCommon>
        </div>
      </Form>
    </div>
  );
}
