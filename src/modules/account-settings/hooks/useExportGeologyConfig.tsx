import accountSettingsRequest from "@/modules/account/api/account.api";
import { useState } from "react";
import { toast } from "react-toastify";

export const useExportGeologyConfig = () => {
  const [loading, setLoading] = useState(false);
  async function request(onSuccess?: Function, onError?: Function) {
    setLoading(true);
    const response = await accountSettingsRequest.exportGeologyConfig();
    if (response.state === "success") {
      onSuccess && onSuccess(response.data);
      const link = response?.data;
      if (link) {
        window.open(link);
      }
      toast.success("Export geology config successfully!");
      setLoading(false);
    } else {
      onError && onError(response);
      toast.error(response?.message ?? "Export geology config failed!");
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
