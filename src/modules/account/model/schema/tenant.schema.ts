import z from "zod";

export const TenantBody = z.object({
  tenancyName: z.string(),
  companyName: z.string().optional(),
  isActive: z.boolean().optional(),
  country: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  postCode: z.string().optional().nullable(),
  suburb: z.string().optional().nullable(),
  firstAddress: z.string().optional().nullable(),
  secondAddress: z.string().optional().nullable(),
  emailAddress: z.any(),
  password: z.any(),
  accountType: z.number().optional(),
  id: z.number().optional(),
});

export type TenantBodyType = z.TypeOf<typeof TenantBody>;
