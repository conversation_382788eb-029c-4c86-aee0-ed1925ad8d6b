"use client";
import AppPermissionDenied from "@/common/@share-components/@shared/AppPermissionDenied";
import { appContants } from "@/common/configs";
import { RequestState } from "@/common/configs/app.contants";
import { appStorage } from "@/common/configs/app.di-container";
import { StoreProvider } from "@/common/vendors/redux/provider/StoreProvider";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import authRequest from "@/modules/auth/api/auth.api";
import { selectUserInfo } from "@/modules/auth/redux/userSlice";
import { log } from "console";
import { usePathname } from "next/navigation";
import React, { useEffect, useState } from "react";
export const PAGEINFO = [
  {
    value: 1,
    paths: [
      "/projects",
      "/prospect-management",
      "/workflows-management",
      "/ai-services",
      "/geology-suite",
      "/geology-field",
      "/rock-style-management",
      "/assay-attribute",
      "/assay-suite",
      "/suites",
      "/attributes",
      "/polygon-management",
      "/export-template",
      "/events",
      "/image-category",
      "/rock-groups",
      "/rock-type-management",
      "/number-fields",
      "/pick-lists",
      "/colours",
      "/units",
      "/rock-select-number",
      "/rock-type-number",
      "/geology-date",
      "/geology-description",
    ],
  },
  {
    value: 2,
    paths: ["/load-images", "/upload-file", "/upload-geology", "/upload-assay"],
  },
  {
    value: 3,
    paths: ["/logging"],
  },
  {
    value: 4,
    paths: ["/prepare-images"],
  },
  {
    value: 5,
    paths: ["/process-images"],
  },
  {
    value: 6,
    paths: ["/process-batch-images"],
  },
];
interface Props {
  permissions: Array<any>;
  children: React.ReactNode;
  redirectBack?: string;
}

const PermissionProvider = ({ children, permissions, redirectBack }: Props) => {
  const pathName = usePathname();
  const roles = useAppSelector((state) => state.user.userInfo.roles);
  const isCompany = roles?.includes("Company");
  const valueOfPathName =
    PAGEINFO.find((item) => item.paths.includes(pathName))?.value ?? 0;
  const userRoleFunctions = useAppSelector(
    (state) => state.user.userInfo.userRoleFunctions
  );

  // if valueOfPathName is not 0, check permission. If valueOfPathName does not exist in userRoleFunctions, show permission denied. If valueOfPathName exists in userRoleFunctions, show all
  const isValid =
    userRoleFunctions?.find((item) => item === valueOfPathName) ||
    valueOfPathName === 0 ||
    isCompany;

  const [isGetPermission, setIsGetPermission] = useState(RequestState.idle);

  const checkPermission = async () => {
    setIsGetPermission(RequestState.pending);
    const currentUser = await authRequest.getCurrentUser();
    if (currentUser.state === RequestState.error) {
      appStorage.removeItem(appContants.tokenKey);
      window.location.href = `/login${
        redirectBack ? `?callbackUrl=${redirectBack}` : ""
      }`;
      return;
    }
    // If not permission, show permission denied
    const findPermission = permissions?.find((permission) =>
      currentUser?.data?.roles?.find(
        (role) => role.toLowerCase() === (permission?.value ?? "").toLowerCase()
      )
    );
    if (!findPermission) {
      setIsGetPermission(RequestState.error);
      return;
    }
    setIsGetPermission(RequestState.success);
  };

  useEffect(() => {
    (async () => await checkPermission())();
  }, []);

  if (
    isGetPermission === RequestState.idle ||
    isGetPermission === RequestState.pending
  ) {
    return (
      <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center"></div>
    );
  }

  if (isGetPermission === RequestState.error || !isValid) {
    return <AppPermissionDenied />;
  }

  return <StoreProvider>{children}</StoreProvider>;
};

export default PermissionProvider;
