import React from "react";

const IconCross = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.8954 9.03295C15.8954 9.21827 15.8548 9.39516 15.7738 9.56299C15.6926 9.73104 15.5884 9.87859 15.461 10.006C15.3333 10.1337 15.183 10.2378 15.0091 10.3188C14.8355 10.3998 14.6558 10.4404 14.4706 10.4404C14.2274 10.4404 13.9955 10.3769 13.7756 10.2494C13.5554 10.1221 13.3817 9.95405 13.2543 9.74551H9.72756V13.2725C9.93589 13.4002 10.1038 13.5739 10.2314 13.7938C10.3587 14.014 10.4225 14.2455 10.4225 14.4888C10.4225 14.6742 10.3847 14.851 10.3096 15.0188C10.2341 15.1869 10.1299 15.3344 9.99676 15.4618C9.86337 15.5896 9.7128 15.6936 9.54507 15.7746C9.37702 15.8556 9.20024 15.8962 9.01502 15.8962C8.8296 15.8962 8.65281 15.8557 8.48507 15.7746C8.31702 15.6936 8.16634 15.5896 8.03338 15.4618C7.89998 15.3345 7.79582 15.1869 7.72059 15.0188C7.64515 14.8509 7.60769 14.6742 7.60769 14.4888C7.60769 14.2456 7.67117 14.014 7.79874 13.7938C7.926 13.5739 8.09404 13.4002 8.30258 13.2725L8.30278 9.74525H4.70638C4.57882 9.95378 4.40493 10.1218 4.18508 10.2491C3.9649 10.3767 3.73338 10.4401 3.4901 10.4401C3.30467 10.4401 3.12788 10.3997 2.96015 10.3185C2.7921 10.2375 2.64142 10.1335 2.50845 10.0057C2.37526 9.87845 2.271 9.73081 2.19566 9.56274C2.12023 9.39499 2.08276 9.21829 2.08276 9.03289C2.08276 8.84787 2.12023 8.66816 2.19566 8.49431C2.2708 8.32054 2.37526 8.17018 2.50845 8.04261C2.64155 7.91515 2.79211 7.81108 2.96015 7.72982C3.12789 7.64886 3.30477 7.60818 3.4901 7.60818C3.73328 7.60818 3.96491 7.67187 4.18508 7.79923C4.40495 7.9268 4.57874 8.09453 4.70638 8.30308H8.30304L8.30294 4.70668C8.09441 4.57921 7.92635 4.40543 7.79909 4.18527C7.67153 3.96549 7.60804 3.73357 7.60804 3.49029C7.60804 3.30527 7.64551 3.12839 7.72094 2.96034C7.79608 2.7926 7.90034 2.64192 8.03373 2.50865C8.16683 2.37555 8.31739 2.27129 8.48543 2.19586C8.65328 2.12072 8.82995 2.08286 9.01538 2.08286C9.2006 2.08286 9.37738 2.12072 9.54542 2.19586C9.71317 2.27119 9.86384 2.37556 9.99712 2.50865C10.1302 2.64204 10.2343 2.79261 10.3099 2.96034C10.385 3.12839 10.4228 3.30517 10.4228 3.49029C10.4228 3.73347 10.359 3.96549 10.2318 4.18527C10.1042 4.40545 9.93625 4.57921 9.72792 4.70668V8.30333H13.2549C13.3824 8.09481 13.556 7.92706 13.7762 7.79949C13.9961 7.67202 14.2279 7.60844 14.4712 7.60844C14.6564 7.60844 14.8361 7.64902 15.0098 7.73008C15.1835 7.81114 15.3339 7.9154 15.4617 8.04287C15.5889 8.17043 15.6931 8.32081 15.7745 8.49457C15.855 8.66804 15.8956 8.84783 15.8956 9.03287L15.8954 9.03295Z"
        fill="#808191"
      />
    </svg>
  );
};

export default IconCross;
