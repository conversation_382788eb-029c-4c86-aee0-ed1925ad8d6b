"use client";
import useGetDetailsAccountSettings from "@/modules/account/hooks/useGetDetailsAccountSettings";
import useUpdateAccountSettings from "@/modules/account/hooks/useUpdateAccountSettings";
import { RequestState } from "@/common/configs/app.contants";
import { useAppDispatch } from "@/common/vendors/redux/store/hook";
import AppLoading from "@/components/common/app-loading";
import { InputTextCommon } from "@/components/common/input-text";
import { SelectCommon } from "@/components/common/select-common";
import { IMAGE_TYPE, Units } from "@/constants/general.const";
import { AccountSettingsBodyType } from "@/modules/account/model/schema/account.schema";
import { useGetListPolygon } from "@/modules/polygon/hooks/useGetListPolygon.hook";
import { Button, Divider, Form, notification } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import * as XLSX from "xlsx";

import { UploadCommon } from "@/components/common/upload-file-common";
import { DataRow } from "@/modules/downhole-point/components/upload-file/upload-geology";
import { DownloadOutlined } from "@ant-design/icons";
import { ColumnsType } from "antd/es/table";
import { useExportGeologyConfig } from "../hooks/useExportGeologyConfig";
import { updateAccountSettings } from "../redux/accountSettingsSlice";
import UploadLogo from "./upload-image";
import { updateCompanyName } from "@/modules/auth/redux/userSlice";
import accountSettingsRequest from "@/modules/account/api/account.api";

const TF_OPTIONS = {
  Yes: {
    value: true,
    label: "Yes",
  },
  No: {
    value: false,
    label: "No",
  },
};

const TableSettings = () => {
  const [isUpdateSetting, setIsUpdateSetting] = useState(false);
  const [isGetDetailSetting, setIsGetDetailSetting] = useState(true);
  const [isGetPolygon, setIsGetPolygons] = useState(true);

  const dispatch = useAppDispatch();

  // API request hooks
  const [requestGetDetailsAccountSettings] = useGetDetailsAccountSettings();
  const [requestUpdateAccountSettings] = useUpdateAccountSettings();
  const [requestGetListPolygons] = useGetListPolygon();

  // Local states
  const [polygons, setPolygons] = useState<{}[]>([]);
  const [boundingPolygons, setBoundingPolygons] = useState<
    {}[] | null | undefined
  >(null);
  const [rowPolygons, setRowPolygons] = useState<{}[] | null | undefined>(null);
  const [fileXlsx, setFileXlsx] = useState<File>();
  const [columns, setColumns] = useState<ColumnsType<DataRow>>([]);
  const [data, setData] = useState<DataRow[]>([]);

  const { control, handleSubmit, setValue, getValues } =
    useForm<AccountSettingsBodyType>({
      defaultValues: {
        id: 0,
        isSelectorArea: false,
        productName: "",
        units: "",
        unitsSymbol: "",
        imageType: "" as any,
        collectionName: "" as any,
        boundingPolygonType: "" as any,
        rowPolygonType: "" as any,
        viewWithBoundingPolygon: "" as any,
        rowPolygon: "" as any,
        logoProduct: "" as any,
        tenantId: "" as any,
        isUseLogo: false,
        collectionNameSingular: "",
      },
    });
  const formGeologyConfig = useForm<any>({});
  const setDefaultValues = (values: AccountSettingsBodyType) => {
    setValue("id", values?.id || (null as any));
    setValue("productName", values?.productName || "Fast Geo");
    setValue("units", values?.units || Units.Metres.value);
    setValue("unitsSymbol", values?.unitsSymbol || "m");
    setValue("imageType", values?.imageType || 1);
    setValue("collectionName", values?.collectionName || "Drillholes");
    setValue(
      "boundingPolygonType",
      values?.boundingPolygonType || (null as any)
    );
    setValue("rowPolygonType", values?.rowPolygonType || (null as any));
    setValue(
      "viewWithBoundingPolygon",
      values?.viewWithBoundingPolygon || false
    );
    setValue("rowPolygon", values?.rowPolygon || false);
    setValue("logoProduct", values?.logoProduct || (null as any));
    setValue("tenantId", values?.tenantId || (null as any));
    setValue("isUseLogo", values?.isUseLogo || (null as any));
    setValue("projectId", values?.projectId || (null as any));
    setValue("prospectId", values?.prospectId || (null as any));
    setValue("drillholeId", values?.drillholeId || (undefined as any));
  };

  const onSubmitSettings = (values: any) => {
    if (values.productName.length > 20) {
      toast.error("Product name must not exceed 20 characters");
    } else {
      requestUpdateAccountSettings(
        {
          ...values,
          projectId: values.projectId ? values.projectId : null,
          prospectId: values.projectId ? values.prospectId : null,
          rowPolygon: values.rowPolygon,
          viewWithBoundingPolygon: values.viewWithBoundingPolygon,
        },
        setIsUpdateSetting,
        (res: any) => {
          dispatch(updateAccountSettings(res.result));
          dispatch(updateCompanyName(res.result.productName));
          toast.success("Update account options successfully", {
            position: "top-center",
          });
        },
        (err: any) => {}
      );
    }
  };

  useEffect(() => {
    requestGetDetailsAccountSettings(
      setIsGetDetailSetting,
      (data: any) => {
        setDefaultValues(data?.result);
        dispatch(updateAccountSettings(data?.result));
      },
      () => {}
    );
    requestGetListPolygons(
      {},
      setIsGetPolygons,
      (data: any) => {
        setPolygons(data?.items);
      },
      () => {}
    );
  }, []);

  useEffect(() => {
    if (polygons) {
      setBoundingPolygons(
        polygons
          ?.filter((polygon: any) => polygon?.type === 1)
          .map((item: any) => {
            return {
              value: item?.id,
              label: item?.name,
            };
          })
      );
      setRowPolygons(
        polygons
          ?.filter((polygon: any) => polygon?.type === 2)
          .map((item: any) => {
            return {
              value: item?.id,
              label: item?.name,
            };
          })
      );
    }
  }, [polygons]);

  const [isUploading, setIsUploading] = useState(false);
  const onSubmitGeologyConfig = async (values: any) => {
    if (!fileXlsx) return;
    setIsUploading(true);
    const response = await accountSettingsRequest.importGeologyConfig({
      file: fileXlsx,
    });
    if (response.state === RequestState.error) {
      notification.error({ message: response.message });
    }
    if (response.state === RequestState.success) {
      toast.success("Import geology config successfully!");
    }

    setIsUploading(false);
  };

  const readExcelFile = async (file: File) => {
    if (!file) return;
    setFileXlsx(file);

    const reader = new FileReader();
    reader.onload = async (e: ProgressEvent<FileReader>) => {
      const binaryStr = e.target?.result;
      if (typeof binaryStr !== "string") return;

      const workbook = XLSX.read(binaryStr, { type: "binary" });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(sheet, {
        header: 1,
      }) as any[][];

      const headers = jsonData[0] as string[];
      const rows = jsonData.slice(1);

      setColumns(
        headers.map((columnName, index) => ({
          title: () => {
            return <div>{columnName}</div>;
          },
          dataIndex: String(index),
          key: String(index),
          render: (text: string) => {
            const numberValue = parseFloat(text);
            if (!isNaN(numberValue)) {
              const roundedValue = numberValue.toFixed(10);
              return parseFloat(roundedValue).toString();
            }

            return text;
          },
        }))
      );
      setData(
        rows.map((row, rowIndex) => {
          const rowData: DataRow = { key: rowIndex };
          row.forEach((cell, i) => {
            rowData[String(i)] = cell;
          });
          return rowData;
        })
      );
    };
    reader.readAsBinaryString(file);
  };

  const {
    request: requestExportGeologyConfig,
    loading: isExportGeologyConfig,
  } = useExportGeologyConfig();

  return (
    <div className="min-h-screen bg-gray-50/30">
      {(isGetDetailSetting || isGetDetailSetting || isGetPolygon) && (
        <AppLoading className={"text-white"} />
      )}
      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-semibold text-gray-900 mb-2">
            Account Options
          </h1>
          <p className="text-gray-500">
            Configure your account settings and preferences
          </p>
          <Divider className="my-6" />
        </div>
        <div className="bg-white p-8 rounded-xl shadow-sm">
          <Form onFinish={handleSubmit(onSubmitSettings)}>
            <div className="flex flex-col gap-2">
              <UploadLogo
                control={control}
                setValue={setValue}
                getValue={getValues}
              />
              <Divider />
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <InputTextCommon
                  label="Product Name"
                  name="productName"
                  placeholder="Enter product name"
                  control={control}
                  size="large"
                />
                <SelectCommon
                  label="Unit Default"
                  name="units"
                  control={control}
                  size="large"
                  options={Object.values(Units)}
                  placeholder="Select unit default"
                />
                <InputTextCommon
                  label="Unit default symbol"
                  name="unitsSymbol"
                  placeholder="Enter default unit symbol"
                  control={control}
                  size="large"
                />
                <InputTextCommon
                  label="Collection Name - Singular"
                  name="collectionNameSingular"
                  placeholder="Enter collection name singular"
                  control={control}
                  size="large"
                />
                <SelectCommon
                  label="Viewing default image type"
                  name="imageType"
                  control={control}
                  size="large"
                  options={Object.values(IMAGE_TYPE)}
                  placeholder="Select default image type"
                />
                <InputTextCommon
                  label="Collection Name - Plural"
                  name="collectionName"
                  control={control}
                  size="large"
                  placeholder="Enter collections name"
                />
                <SelectCommon
                  label="Default Bounding Polygon"
                  name="boundingPolygonType"
                  control={control}
                  size="large"
                  options={boundingPolygons}
                  placeholder="Select default bounding polygon"
                />
                <SelectCommon
                  label="Default Row Polygon"
                  name="rowPolygonType"
                  control={control}
                  size="large"
                  options={rowPolygons}
                  placeholder="Select default row polygon"
                />

                <SelectCommon
                  name="viewWithBoundingPolygon"
                  label="View with Bounding Polygon"
                  control={control}
                  size="large"
                  options={Object.values(TF_OPTIONS)}
                  placeholder="Select an option"
                />

                <SelectCommon
                  name="rowPolygon"
                  label="View with Row Polygon"
                  control={control}
                  size="large"
                  options={Object.values(TF_OPTIONS)}
                  placeholder="Select an option"
                />
              </div>
              <div className="flex justify-center gap-2 mt-8 pb-6 border-b">
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  loading={isUpdateSetting}
                  className="min-w-[120px] h-12 text-base font-medium"
                >
                  Save Changes
                </Button>
              </div>
            </div>
          </Form>

          <div className="mt-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Account Configuration Export/Import
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={() => requestExportGeologyConfig()}
                  loading={isExportGeologyConfig}
                  size="large"
                  className="w-full h-12 flex items-center justify-center text-base shadow-sm hover:shadow-md transition-shadow"
                  type="primary"
                >
                  Export Account Config
                </Button>
              </div>
              <div className="bg-gray-50/50 p-6 rounded-lg border border-gray-100">
                <Form
                  className="space-y-4"
                  onFinish={formGeologyConfig.handleSubmit(
                    onSubmitGeologyConfig
                  )}
                >
                  <UploadCommon
                    name="ExcelFile"
                    control={formGeologyConfig.control}
                    label="Import Configuration File"
                    placeholder="Select Excel file"
                    file={fileXlsx}
                    onUploadAsync={async (file) => {
                      readExcelFile(file);
                    }}
                  />

                  <Button
                    disabled={!fileXlsx}
                    type="primary"
                    htmlType="submit"
                    size="large"
                    loading={isUploading}
                    className="w-full h-12 text-base shadow-sm hover:shadow-md transition-shadow"
                  >
                    Import Configuration
                  </Button>
                </Form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TableSettings;
