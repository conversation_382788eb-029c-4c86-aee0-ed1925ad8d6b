import { useState } from "react";
import mobileProfileRequest from "../api/mobile-profile.api";
import { MobileProfileQuery } from "../interface/mobile-profile.query";

export const useGetListMobileProfile = () => {
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const request = async (
    params: MobileProfileQuery,
    onSuccess?: Function,
    onError?: Function,
    forceUpdate?: boolean
  ) => {
    setLoading(true);
    const response = await mobileProfileRequest.getList({
      ...params,
      isActive: params?.isActive ?? true,
    });
    if (response?.state === "success") {
      setData(response.data?.items);
      setTotal(response.data?.pagination?.total);
      setLoading(false);
      onSuccess && onSuccess(response.data);
      return response.data;
    } else {
      onError && onError(response);
      setLoading(false);
      return null;
    }
  };

  return { request, loading, data, total };
};
