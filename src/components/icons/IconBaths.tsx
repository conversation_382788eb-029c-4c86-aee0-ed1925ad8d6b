import React from "react";

const IconBaths = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.93245 3.99997C6.11497 3.99997 5.44597 4.66901 5.44597 5.48642C5.44597 5.83789 5.56978 6.16022 5.77581 6.41552C5.33151 6.53392 5 6.93916 5 7.41881V10.6891C5 11.1011 5.2417 11.4552 5.59465 11.6274V13.662C5.59465 14.398 6.19646 14.9998 6.93253 14.9998C7.6686 14.9998 8.27041 14.3981 8.27041 13.662V11.6274C8.62337 11.4551 8.86506 11.1011 8.86506 10.6891V7.41881C8.86506 6.93916 8.53357 6.53384 8.08925 6.41541C8.29528 6.16023 8.41909 5.83781 8.41909 5.48632C8.41909 4.66888 7.74983 3.99997 6.93245 3.99997ZM11.7705 3.99997C10.953 3.99997 10.284 4.66901 10.284 5.48642C10.284 5.85689 10.4201 6.19624 10.6463 6.45733C10.2769 6.60604 9.98664 6.9598 9.98664 7.41889V9.05406C9.98664 9.20267 9.96601 9.33789 10.0563 9.51854C10.0808 9.56742 10.1368 9.59835 10.1818 9.63934L9.57323 10.8517C9.52647 10.9439 9.53082 11.0537 9.58484 11.1418C9.63886 11.2299 9.73467 11.2836 9.83805 11.2837H10.4327V13.6621C10.4349 14.3971 11.0345 15 11.7706 15C12.5067 15 13.1063 14.3971 13.1085 13.6621V11.2837H13.7031C13.8065 11.2836 13.9022 11.2299 13.9562 11.1418C14.0101 11.0537 14.0146 10.9439 13.9677 10.8517L13.3592 9.63934C13.4041 9.59835 13.4602 9.56742 13.4846 9.51854C13.5749 9.33787 13.5543 9.20265 13.5543 9.05406V7.41889C13.5543 6.9598 13.2641 6.60602 12.8946 6.45733C13.121 6.19634 13.2569 5.85689 13.2569 5.48642C13.2569 4.66898 12.5879 3.99997 11.7705 3.99997ZM6.93245 4.5946C7.42859 4.5946 7.82439 4.99038 7.82439 5.48653C7.82439 5.98268 7.42862 6.37846 6.93245 6.37846C6.43631 6.37846 6.04051 5.98268 6.04051 5.48653C6.04051 4.99038 6.43628 4.5946 6.93245 4.5946ZM11.7705 4.5946C12.2667 4.5946 12.6625 4.99038 12.6625 5.48653C12.6625 5.98268 12.2667 6.37846 11.7705 6.37846C11.2744 6.37846 10.8786 5.98268 10.8786 5.48653C10.8786 4.99038 11.2744 4.5946 11.7705 4.5946ZM6.04056 6.97298H7.82442C8.07659 6.97298 8.27039 7.16673 8.27039 7.41889V10.6892C8.27039 10.909 8.10801 11.1 7.91265 11.1398C7.77412 11.1685 7.675 11.2909 7.67572 11.4324V13.6622C7.67572 14.0791 7.34941 14.4055 6.93248 14.4055C6.51555 14.4055 6.18924 14.0791 6.18924 13.6622V11.4324C6.18997 11.2909 6.09084 11.1684 5.95231 11.1397C5.75696 11.0999 5.59458 10.9089 5.59458 10.6891V7.41879C5.59458 7.16662 5.78839 6.97298 6.04056 6.97298ZM11.0273 6.97298H12.5138C12.7307 6.97298 12.9597 7.13694 12.9597 7.41889V9.05406C12.9597 9.16469 12.9546 9.18461 12.9551 9.20267C12.854 9.20516 12.761 9.25891 12.7083 9.34536C12.6557 9.4318 12.6506 9.539 12.695 9.63001L13.2245 10.6891H12.8111C12.6469 10.6892 12.5139 10.8223 12.5138 10.9864V13.6574V13.6619C12.51 14.0776 12.1858 14.4052 11.7705 14.4052C11.3552 14.4052 11.0309 14.0776 11.0273 13.6619V10.9865C11.0273 10.8223 10.8942 10.6893 10.73 10.6892H10.3166L10.8462 9.63006C10.8904 9.53905 10.8853 9.43186 10.8328 9.34541C10.7801 9.25896 10.6872 9.20521 10.586 9.20272C10.5864 9.18466 10.5813 9.16474 10.5813 9.05411V7.41894C10.5813 7.137 10.8104 6.97298 11.0273 6.97298Z"
        fill="#808191"
      />
    </svg>
  );
};

export default IconBaths;
