import http from "@/lib/http";
import { AccountBodyType } from "@/modules/account/model/schema/account.schema";

const userRequest = {
  createUser: async (body: AccountBodyType) => {
    try {
      return http.post(`/services/app/User/Create`, {
        ...body,
        isActive: body.isActive === true ? true : false,
      });
    } catch (error) {
      return Promise.reject(error);
    }
  },

  getListUser: async (params: {
    keyword?: string;
    isActive?: boolean;
    skipCount: number;
    maxResultCount: number;
  }) => {
    try {
      return http.get(`/services/app/User/GetUsers`, { params });
    } catch (error) {
      return Promise.reject(error);
    }
  },
  getListUserSystemAdmin: async (params: {
    keyword?: string;
    isActive?: boolean;
    skipCount: number;
    maxResultCount: number;
  }) => {
    try {
      return http.get(`/services/app/User/GetAll`, { params });
    } catch (error) {
      return Promise.reject(error);
    }
  },
  updateUser: async (body: AccountBodyType) => {
    try {
      return http.put(`/services/app/User/Update`, body);
    } catch (error) {
      return Promise.reject(error);
    }
  },
  deleteUser: async (id: string) => {
    try {
      return http.delete(`/services/app/User/Delete`, { params: { id } });
    } catch (error) {
      return Promise.reject(error);
    }
  },
  getUserById: async (id: string) => {
    try {
      return http.get(`/services/app/User/GetUser`, { params: { id } });
    } catch (error) {
      return Promise.reject(error);
    }
  },
  setAdmin: async (params: { id: string | number }) => {
    try {
      return http.post(`/services/app/User/SetCompanyRole?userId=${params.id}`);
    } catch (error) {
      return Promise.reject(error);
    }
  },
  deleteAdmin: async (params: { id: string | number }) => {
    try {
      return http.delete(
        `/services/app/User/DeleteCompanyRole?userId=${params.id}`
      );
    } catch (error) {
      return Promise.reject(error);
    }
  },
};

export default userRequest;
