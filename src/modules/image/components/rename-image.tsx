"use client";
import { ButtonCommon } from "@/components/common/button-common";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "antd";
import { isEmpty } from "lodash";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { UploadFiles } from "../interface/image.interface";

interface FormUploadFiles {
  newName?: string;
}

export const UploadImageValidation = z.object({
  newName: z.string(),
});

type RenameImageProps = {
  fileChange: UploadFiles | undefined;
  uploadFiles: any[];
  setUploadFiles: any;
  checkValidFile: (newFiles: File[], originalFiles?: UploadFiles[]) => void;
  onCancel: () => void;
};

const RenameImage: React.FC<RenameImageProps> = ({
  fileChange,
  uploadFiles,
  onCancel,
  checkValidFile,
  setUploadFiles,
}) => {
  const { handleSubmit, control, setValue } = useForm<FormUploadFiles>({
    resolver: zodResolver(UploadImageValidation),
  });

  const onSubmitName = async (value) => {
    const mimeToExtension: { [key: string]: string } = {
      "image/jpeg": ".jpg",
      "image/png": ".png",
      "image/gif": ".gif",
      "image/bmp": ".bmp",
      "image/webp": ".webp",
      "image/tiff": ".tiff",
      "image/svg+xml": ".svg",
      "image/x-icon": ".ico",
    };
    const file = fileChange?.file;
    const extension = mimeToExtension[file.type];
    const newFile = new File([file], value.newName + extension, {
      type: file.type,
    });
    const removedOldFiles = uploadFiles.filter(
      (uploadFile) =>
        uploadFile?.file?.name !== fileChange?.file?.name &&
        uploadFile?.url !== fileChange?.url
    );

    await checkValidFile([newFile], removedOldFiles);

    onCancel();
  };

  useEffect(() => {
    if (!isEmpty(fileChange?.file?.name)) {
      setValue("newName", fileChange?.file?.name.replace(/\.[^/.]+$/, ""));
    }
  }, [fileChange]);

  return (
    <div className="px-6 flex flex-col gap-4">
      <p className="font-bold text-24-28 capitalize text-center font-visby">
        Rename file
      </p>
      <Form
        onFinish={handleSubmit(onSubmitName)}
        className="flex flex-col gap-3"
      >
        <InputTextCommon
          label="Name"
          name="newName"
          placeholder="Enter new name"
          control={control}
        />
        <div className="flex flex-col gap-3 mt-3">
          <ButtonCommon
            type="submit"
            className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
          >
            Rename
          </ButtonCommon>
          <ButtonCommon
            onClick={() => {
              onCancel();
            }}
            className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
          >
            Cancel
          </ButtonCommon>
        </div>
      </Form>
    </div>
  );
};
export default RenameImage;
